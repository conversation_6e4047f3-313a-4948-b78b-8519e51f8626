# Vector Tile业务层编码重构

## 概述

本次重构将Vector Tile的PBF编码处理从SQL层移动到业务层（Service Impl），实现了更灵活的数据处理和更好的代码可维护性。

## 修改内容

### 1. 数据传输对象（DTO）

**新增文件：** `TileEventDTO.java`
- 用于承载从数据库查询的原始几何数据和属性
- 包含事件的所有必要字段：id、event_status、discovery_time、waste_material等
- 包含经过ST_AsMVTGeom处理的几何数据

### 2. 数据访问层（Mapper）修改

**修改文件：** `EventMapper.java` 和 `EventMapper.xml`

**原实现：**
```sql
SELECT ST_AsMVT(mvtgeom.*, 'events_layer', 4096, 'geom')
FROM mvtgeom;
```

**新实现：**
```sql
SELECT
    e.id,
    e.event_status,
    e.discovery_time,
    e.waste_material,
    -- 其他字段...
    ST_AsMVTGeom(
        ST_Transform(e.location, #{targetSrid}),
        b.mercator_geom,
        4096, 256, true
    ) AS geom
FROM public.event e, bounds b
WHERE e.is_deleted = 0
  AND e.location && b.wgs84_geom
```

**变化：**
- 移除了外层的`ST_AsMVT`函数调用
- 返回类型从`Byte[]`改为`List<TileEventDTO>`
- 方法名从`getEventTileAsPbf`改为`getEventTileData`

### 3. 业务层（Service）重构

**修改文件：** `TileServiceImpl.java`

**新增依赖：**
```xml
<dependency>
    <groupId>com.wdtinc</groupId>
    <artifactId>mapbox-vector-tile</artifactId>
    <version>3.1.0</version>
</dependency>
```

**核心变化：**
1. **数据获取：** 调用`eventMapper.getEventTileData()`获取原始数据
2. **PBF编码：** 新增`generatePbfTile()`方法在业务层进行编码
3. **属性处理：** 在业务层构建完整的属性映射

### 4. PBF编码实现

**新增方法：** `generatePbfTile(List<TileEventDTO> tileEvents)`

**处理流程：**
1. 创建瓦片边界（4096x4096像素）
2. 提取几何数据和属性
3. 使用Mapbox Vector Tile库进行编码
4. 返回PBF格式的二进制数据

**属性映射：**
```java
Map<String, Object> props = new HashMap<>();
props.put("id", event.getId());
props.put("event_status", event.getEventStatus());
props.put("discovery_time", event.getDiscoveryTime().getTime());
props.put("waste_material", event.getWasteMaterial());
// ... 其他属性
```

## 架构优势

### 1. 分离关注点
- **SQL层：** 专注于数据查询和几何处理
- **业务层：** 负责PBF编码和属性处理
- **控制层：** 处理HTTP响应

### 2. 灵活性提升
- 可以在业务层对属性进行复杂处理
- 支持动态图层配置
- 便于添加缓存策略

### 3. 可维护性
- 代码逻辑更清晰
- 便于单元测试
- 易于扩展新功能

### 4. 性能考虑
- SQL查询更简单，减少数据库负载
- 业务层可以实现更精细的缓存控制
- 支持异步处理

## 测试

**新增测试文件：** `TileServiceTest.java`

**测试用例：**
1. `testGetEventTile()` - 基本瓦片生成测试
2. `testMultipleTileLevels()` - 多缩放级别测试

## 使用方法

### 1. API调用
```http
GET /tile/events/{z}/{x}/{y}.pbf
```

### 2. 响应格式
- Content-Type: `application/vnd.mapbox-vector-tile`
- 返回PBF格式的二进制数据

### 3. 前端使用
```javascript
// Mapbox GL JS
map.addSource('events', {
    'type': 'vector',
    'tiles': ['http://localhost:8080/tile/events/{z}/{x}/{y}.pbf']
});

map.addLayer({
    'id': 'events-layer',
    'type': 'circle',
    'source': 'events',
    'source-layer': 'events_layer',
    'paint': {
        'circle-radius': 6,
        'circle-color': '#ff0000'
    }
});
```

## 配置参数

```yaml
map-services:
  tile:
    target-srid: 3857  # 目标坐标系
```

## 注意事项

1. **依赖更新：** 确保添加了mapbox-vector-tile依赖
2. **数据库连接：** 需要PostGIS扩展支持
3. **内存使用：** 大量数据时注意内存管理
4. **缓存策略：** 建议在生产环境中添加瓦片缓存

## 兼容性

- **向后兼容：** API接口保持不变
- **数据格式：** PBF输出格式完全兼容
- **前端代码：** 无需修改现有前端代码

## 后续优化建议

1. **缓存机制：** 添加Redis缓存支持
2. **异步处理：** 实现异步瓦片生成
3. **压缩优化：** 添加gzip压缩
4. **监控指标：** 添加性能监控
5. **批量处理：** 支持批量瓦片生成