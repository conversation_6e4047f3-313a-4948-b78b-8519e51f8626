package org.springblade.modules.beachwaste.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springblade.modules.beachwaste.mapper.EventMapper;
import org.springblade.modules.beachwaste.pojo.vo.VectorTile;
import org.springblade.modules.beachwaste.service.ITileService;

/**
 * 瓦片服务实现类
 * 实现动态矢量瓦片生成功能，支持高性能地图数据渲染
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TileServiceImpl implements ITileService {

    private final EventMapper eventMapper;

    /**
     * 从配置文件中读取目标坐标系SRID
     * 默认使用Web Mercator投影(SRID: 3857)
     */
    @Value("${map-services.tile.target-srid:3857}")
    private int targetSrid;

    /**
     * 获取事件数据的矢量瓦片
     *
     * @param z 地图缩放级别
     * @param x 瓦片X坐标
     * @param y 瓦片Y坐标
     * @return PBF格式的二进制数据
     */
    @Override
    public VectorTile getEventTile(int z, int x, int y) {
        try {
            log.info("生成事件矢量瓦片: z={}, x={}, y={}, targetSrid={}", z, x, y, targetSrid);

            // 调用Mapper获取瓦片数据
            VectorTile vectorTile = eventMapper.getEventTileAsPbf(z, x, y, targetSrid);

            if (vectorTile != null && vectorTile.hasData()) {
                log.info("成功生成矢量瓦片，数据大小: {} bytes", vectorTile.getDataSize());
                return vectorTile;
            } else {
                log.error("指定区域无事件数据，返回空瓦片");
                return new VectorTile(new byte[0]);
            }

        } catch (Exception e) {
            log.error("生成事件矢量瓦片失败: z={}, x={}, y={}, targetSrid={}", z, x, y, targetSrid, e);
            throw new RuntimeException("矢量瓦片生成失败: " + e.getMessage(), e);
        }
    }

}
