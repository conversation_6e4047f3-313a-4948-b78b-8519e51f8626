package org.springblade.modules.beachwaste.pojo.dto;

import lombok.Data;
import org.locationtech.jts.geom.Geometry;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 瓦片事件数据传输对象
 * 用于承载从数据库查询的原始几何数据和属性，供业务层进行PBF编码
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class TileEventDTO {

    /**
     * 事件唯一标识符
     */
    private Long id;

    /**
     * 当前处理状态 ID
     */
    private Long eventStatus;

    /**
     * 事件发现时间（UTC+8）
     */
    private Date discoveryTime;

    /**
     * 垃圾材质类型
     */
    private Long wasteMaterial;

    /**
     * 垃圾尺寸分类
     */
    private Long wasteSize;

    /**
     * 发现方式 枚举类（0-AI/1-人工）
     */
    private Long discoveryMethod;

    /**
     * AI 识别置信度值
     */
    private BigDecimal confidence;

    /**
     * 处理人员id
     */
    private Long handlerStaffId;

    /**
     * 经过ST_AsMVTGeom处理后的几何数据
     * 已转换为瓦片坐标系的几何对象
     */
    private Geometry geom;

}