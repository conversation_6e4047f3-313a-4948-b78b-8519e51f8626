package org.springblade.modules.beachwaste.pojo.vo;

import lombok.Data;

/**
 * 矢量瓦片数据传输对象
 * 用于封装PostGIS ST_AsMVT函数返回的二进制矢量瓦片数据
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Data
public class VectorTile {

    /**
     * 矢量瓦片的二进制数据（PBF格式）
     * 对应PostGIS ST_AsMVT函数返回的bytea类型
     */
    private byte[] mvt;

    /**
     * 默认构造函数
     */
    public VectorTile() {
    }

    /**
     * 构造函数
     *
     * @param mvt 矢量瓦片二进制数据
     */
    public VectorTile(byte[] mvt) {
        this.mvt = mvt;
    }

    /**
     * 获取矢量瓦片数据
     *
     * @return 二进制数据，如果无数据则返回null
     */
    public byte[] getMvt() {
        return mvt;
    }

    /**
     * 设置矢量瓦片数据
     *
     * @param mvt 二进制数据
     */
    public void setMvt(byte[] mvt) {
        this.mvt = mvt;
    }

    /**
     * 检查是否包含有效数据
     *
     * @return 如果包含数据返回true，否则返回false
     */
    public boolean hasData() {
        return mvt != null && mvt.length > 0;
    }

    /**
     * 获取数据大小
     *
     * @return 数据字节数，如果无数据返回0
     */
    public int getDataSize() {
        return mvt != null ? mvt.length : 0;
    }
}
