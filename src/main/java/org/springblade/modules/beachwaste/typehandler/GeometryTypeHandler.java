package org.springblade.modules.beachwaste.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.postgis.PGgeometry;
import org.postgresql.util.PGobject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * Geometry类型处理器
 * 用于处理JTS Geometry对象与PostGIS几何类型之间的转换
 * 支持所有JTS几何类型（Point、LineString、Polygon、MultiPoint等）
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@MappedJdbcTypes(JdbcType.OTHER)
@MappedTypes(Geometry.class)
public class GeometryTypeHandler extends BaseTypeHandler<Geometry> {

    private static final Logger logger = LoggerFactory.getLogger(GeometryTypeHandler.class);
    private static final WKTReader wktReader = new WKTReader();
    private static final WKTWriter wktWriter = new WKTWriter();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Geometry parameter, JdbcType jdbcType) throws SQLException {
        try {
            // Convert JTS Geometry to WKT string and create PGobject
            String wkt = wktWriter.write(parameter);
            PGobject pgObject = new PGobject();
            pgObject.setType("geometry");
            pgObject.setValue(wkt);
            ps.setObject(i, pgObject);

            if (logger.isDebugEnabled()) {
                logger.debug("Set Geometry parameter: {} as WKT: {}", parameter.getGeometryType(), wkt);
            }
        } catch (Exception e) {
            logger.error("Error setting Geometry parameter: {}", e.getMessage(), e);
            throw new SQLException("Failed to set Geometry parameter", e);
        }
    }

    @Override
    public Geometry getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseGeometry(rs.getObject(columnName), columnName);
    }

    @Override
    public Geometry getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseGeometry(rs.getObject(columnIndex), "column_" + columnIndex);
    }

    @Override
    public Geometry getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseGeometry(cs.getObject(columnIndex), "column_" + columnIndex);
    }

    /**
     * 解析几何对象
     * 支持多种PostGIS返回格式：PGgeometry、PGobject、String(WKT)、byte[](二进制)
     *
     * @param obj 数据库返回的对象
     * @param identifier 列标识符（用于日志）
     * @return JTS Geometry对象
     * @throws SQLException SQL异常
     */
    private Geometry parseGeometry(Object obj, String identifier) throws SQLException {
        if (obj == null) {
            if (logger.isDebugEnabled()) {
                logger.debug("Geometry {} is null", identifier);
            }
            return null;
        }

        try {
            if (logger.isDebugEnabled()) {
                logger.debug("Parsing geometry {} of type: {}", identifier, obj.getClass().getName());
            }

            if (obj instanceof PGgeometry) {
                // PostGIS PGgeometry对象 - 最佳情况
                PGgeometry pgGeometry = (PGgeometry) obj;
                org.postgis.Geometry postgisGeom = pgGeometry.getGeometry();
                String wkt = postgisGeom.toString();
                
                if (logger.isDebugEnabled()) {
                    logger.debug("Parsed PGgeometry {} to WKT: {}", identifier, wkt);
                }
                
                return wktReader.read(wkt);
                
            } else if (obj instanceof PGobject) {
                // PostgreSQL PGobject对象
                PGobject pgObject = (PGobject) obj;
                String value = pgObject.getValue();
                
                if (logger.isDebugEnabled()) {
                    logger.debug("Parsed PGobject {} (type: {}) value: {}", 
                        identifier, pgObject.getType(), value);
                }
                
                if (value == null || value.trim().isEmpty()) {
                    logger.warn("Empty value in PGobject for geometry {}", identifier);
                    return null;
                }
                
                return wktReader.read(value);
                
            } else if (obj instanceof String) {
                // 直接的WKT字符串
                String wkt = (String) obj;
                
                if (logger.isDebugEnabled()) {
                    logger.debug("Using String {} as WKT: {}", identifier, wkt);
                }
                
                if (wkt.trim().isEmpty()) {
                    logger.warn("Empty WKT string for geometry {}", identifier);
                    return null;
                }
                
                return wktReader.read(wkt);
                
            } else if (obj instanceof byte[]) {
                // 二进制几何数据 - 需要特殊处理
                byte[] bytes = (byte[]) obj;
                logger.warn("Received binary geometry data for {}, length: {} bytes. " +
                    "This might be MVT format which should be handled differently.", 
                    identifier, bytes.length);
                
                // 对于二进制数据，我们无法直接转换为JTS Geometry
                // 这通常表示配置错误，应该在SQL层面返回WKT格式
                throw new SQLException("Binary geometry data detected for " + identifier + 
                    ". Please ensure SQL query returns WKT format, not binary MVT format.");
                
            } else {
                // 未知类型，尝试转换为字符串
                String wkt = obj.toString();
                logger.warn("Unknown geometry type {} for {}: {}, attempting to parse as WKT", 
                    obj.getClass().getName(), identifier, wkt);
                
                if (wkt.trim().isEmpty()) {
                    logger.warn("Empty string representation for geometry {}", identifier);
                    return null;
                }
                
                return wktReader.read(wkt);
            }

        } catch (ParseException e) {
            logger.error("Failed to parse WKT for geometry {}: {}. Raw data type: {}, value: {}", 
                identifier, e.getMessage(), obj.getClass().getName(), obj);
            throw new SQLException("Invalid WKT format for geometry " + identifier + ": " + e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Unexpected error parsing geometry {}: {}. Raw data type: {}, value: {}", 
                identifier, e.getMessage(), obj.getClass().getName(), obj);
            throw new SQLException("Failed to parse geometry " + identifier + ": " + e.getMessage(), e);
        }
    }
}