package org.springblade.modules.beachwaste.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.beachwaste.pojo.vo.VectorTile;
import org.springblade.modules.beachwaste.service.ITileService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 瓦片服务控制器
 * 提供动态矢量瓦片API接口，支持前端地图库高效渲染海量数据点
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/tiles")
@Tag(name = "瓦片服务", description = "动态矢量瓦片API接口")
public class TileController {

    private final ITileService tileService;

    /**
     * 获取事件数据的矢量瓦片
     * 根据指定的瓦片坐标(z,x,y)，返回符合Mapbox Vector Tile规范的PBF格式数据
     *
     * @param z 地图缩放级别，范围通常为0-18
     * @param x 瓦片X坐标
     * @param y 瓦片Y坐标
     * @return PBF格式的矢量瓦片数据
     */
    @GetMapping(value = "/events/{z}/{x}/{y}.pbf", produces = "application/vnd.mapbox-vector-tile")
    @Operation(
        summary = "获取事件瓦片数据",
        description = "根据指定的瓦片坐标(z,x,y)，获取event事件数据的矢量瓦片，返回PBF格式数据用于前端地图渲染"
    )
    public ResponseEntity<byte[]> getEventTile(
            @Parameter(description = "地图缩放级别", example = "10")
            @PathVariable int z,
            @Parameter(description = "瓦片X坐标", example = "512")
            @PathVariable int x,
            @Parameter(description = "瓦片Y坐标", example = "256")
            @PathVariable int y) {

        try {

            // 参数验证
            if (z < 0 || z > 18) {
                return ResponseEntity.badRequest().build();
            }

            // 获取瓦片数据
            VectorTile vectorTile = tileService.getEventTile(z, x, y);

            // 检查是否有数据
            if (vectorTile == null || !vectorTile.hasData()) {
                return ResponseEntity.noContent().build();
            }

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/x-protobuf"));
			// 缓存1小时
            headers.setCacheControl("public, max-age=3600");
            headers.add("Access-Control-Allow-Origin", "*");

            return ResponseEntity.ok().headers(headers).body(vectorTile.getMvt());

        } catch (Exception e) {
            log.error("获取瓦片数据失败: z={}, x={}, y={}, error={}", z, x, y, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }
}
