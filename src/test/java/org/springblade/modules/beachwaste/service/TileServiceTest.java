package org.springblade.modules.beachwaste.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 瓦片服务测试类
 * 用于测试修改后的vector tile生成功能
 *
 * <AUTHOR>
 * @since 2025-01-01
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class TileServiceTest {

    @Autowired
    private ITileService tileService;

    /**
     * 测试事件瓦片生成
     */
    @Test
    public void testGetEventTile() {
        try {
            // 测试获取瓦片数据
            byte[] tileData = tileService.getEventTile(10, 512, 512);
            
            if (tileData != null && tileData.length > 0) {
                log.info("成功生成瓦片数据，大小: {} bytes", tileData.length);
            } else {
                log.info("瓦片区域内无数据");
            }
            
        } catch (Exception e) {
            log.error("瓦片生成测试失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 测试多个瓦片级别
     */
    @Test
    public void testMultipleTileLevels() {
        int[] zoomLevels = {8, 10, 12, 14};
        
        for (int z : zoomLevels) {
            try {
                byte[] tileData = tileService.getEventTile(z, 128, 128);
                log.info("缩放级别 {} 瓦片数据大小: {} bytes", z, tileData != null ? tileData.length : 0);
            } catch (Exception e) {
                log.error("缩放级别 {} 瓦片生成失败: {}", z, e.getMessage());
            }
        }
    }
}